/**
 * Glasses Virtual Try-On Module for VirtualTryOn
 * Handles TensorFlow.js face detection and glasses overlay
 */

class GlassesVTO {
    constructor() {
        this.video = null;
        this.canvas = null;
        this.ctx = null;
        this.model = null;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.glassesMesh = null;
        this.isActive = false;
        this.animationFrameId = null;
        this.selectedGlassesIndex = 0;
        
        // Glasses catalog - will be loaded from API
        this.glassesItems = [];
    }

    /**
     * Load glasses catalog from API
     */
    async loadGlassesCatalog() {
        try {
            const response = await fetch('/api/glasses-catalog');
            const glassesData = await response.json();
            this.glassesItems = glassesData;
            console.log('Loaded glasses catalog:', this.glassesItems.length, 'items');
        } catch (error) {
            console.error('Failed to load glasses catalog:', error);
            // Fallback to empty array
            this.glassesItems = [];
        }
    }

    /**
     * Initialize Glasses VTO system
     */
    async init() {
        try {
            // Check camera access
            const cameraSupport = await window.threeUtils.checkCameraAccess();
            if (!cameraSupport.supported) {
                this.showError('Camera access required: ' + cameraSupport.reason);
                return false;
            }

            // Load glasses catalog
            await this.loadGlassesCatalog();

            // Initialize TensorFlow.js
            await this.initTensorFlow();

            // Setup video and canvas
            this.setupVideoCanvas();

            // Initialize Three.js for glasses overlay
            this.initThreeJS();

            // Setup glasses selection UI
            this.setupGlassesSelection();
            
            console.log('Glasses VTO system initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize Glasses VTO:', error);
            this.showError('Failed to initialize glasses try-on: ' + error.message);
            return false;
        }
    }

    /**
     * Initialize TensorFlow.js and face detection model
     */
    async initTensorFlow() {
        const loadingIndicator = document.getElementById('glasses-loading');
        if (loadingIndicator) loadingIndicator.style.display = 'block';
        
        try {
            // Initialize TensorFlow.js backend
            await tf.ready();
            
            // Load face landmarks detection model
            this.model = await faceLandmarksDetection.load(
                faceLandmarksDetection.SupportedPackages.mediapipeFacemesh,
                { maxFaces: 1 }
            );
            
            console.log('TensorFlow.js face detection model loaded');
        } catch (error) {
            throw new Error('Failed to load face detection model: ' + error.message);
        } finally {
            if (loadingIndicator) loadingIndicator.style.display = 'none';
        }
    }

    /**
     * Setup video and canvas elements
     */
    setupVideoCanvas() {
        this.video = document.getElementById('glasses-video');
        this.canvas = document.getElementById('glasses-canvas');
        
        if (!this.video || !this.canvas) {
            throw new Error('Video or canvas element not found');
        }
        
        this.ctx = this.canvas.getContext('2d');
        
        // Setup video constraints
        this.video.width = 800;
        this.video.height = 600;
        this.canvas.width = 800;
        this.canvas.height = 600;
    }

    /**
     * Initialize Three.js for glasses overlay
     */
    initThreeJS() {
        const container = document.getElementById('glasses-overlay');
        if (!container) {
            throw new Error('Glasses overlay container not found');
        }

        // Create scene for glasses rendering
        const { scene, camera, renderer } = window.threeUtils.createScene('glasses-overlay', {
            alpha: true,
            lighting: false
        });

        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;

        // Position camera for 2D overlay
        this.camera.position.z = 5;
        
        // Create initial glasses mesh
        this.createGlassesMesh();
    }

    /**
     * Create glasses mesh for overlay
     */
    async createGlassesMesh() {
        try {
            const glassesItem = this.glassesItems[this.selectedGlassesIndex];
            const texture = await window.threeUtils.loadTexture(glassesItem.texture);
            
            texture.colorSpace = THREE.SRGBColorSpace;
            
            const geometry = new THREE.PlaneGeometry(2, 1);
            const material = new THREE.MeshBasicMaterial({ 
                map: texture, 
                transparent: true,
                alphaTest: 0.1
            });
            
            if (this.glassesMesh) {
                this.scene.remove(this.glassesMesh);
            }
            
            this.glassesMesh = new THREE.Mesh(geometry, material);
            this.scene.add(this.glassesMesh);
            
        } catch (error) {
            console.warn('Failed to load glasses texture, using placeholder');
            // Create placeholder glasses
            const geometry = new THREE.PlaneGeometry(2, 1);
            const material = new THREE.MeshBasicMaterial({ 
                color: 0x333333, 
                transparent: true, 
                opacity: 0.7 
            });
            
            if (this.glassesMesh) {
                this.scene.remove(this.glassesMesh);
            }
            
            this.glassesMesh = new THREE.Mesh(geometry, material);
            this.scene.add(this.glassesMesh);
        }
    }

    /**
     * Start camera and face detection
     */
    async startCamera() {
        try {
            // Check device capabilities
            const capabilities = window.threeUtils.getDeviceCapabilities();
            if (!capabilities.isHTTPS && location.hostname !== 'localhost') {
                throw new Error('HTTPS is required for camera access');
            }

            // Adaptive video constraints based on device
            const constraints = {
                video: {
                    width: capabilities.isMobile ? 640 : 800,
                    height: capabilities.isMobile ? 480 : 600,
                    facingMode: 'user',
                    frameRate: capabilities.isMobile ? 15 : 30
                }
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);

            this.video.srcObject = stream;
            this.video.play();

            this.isActive = true;

            // Start performance monitoring
            window.performanceMonitor.start();

            this.detectAndRender();

            // Update UI
            const startButton = document.getElementById('start-glasses-btn');
            if (startButton) {
                startButton.textContent = 'Stop Camera';
                startButton.classList.add('bg-red-500');
                startButton.classList.remove('bg-blue-500');
            }

            console.log('Camera started for glasses try-on');
        } catch (error) {
            console.error('Failed to start camera:', error);
            let errorMessage = 'Failed to access camera: ';

            if (error.name === 'NotAllowedError') {
                errorMessage += 'Camera permission denied. Please allow camera access and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No camera found on this device.';
            } else if (error.message.includes('HTTPS')) {
                errorMessage += 'HTTPS is required for camera access.';
            } else {
                errorMessage += error.message;
            }

            this.showError(errorMessage);
        }
    }

    /**
     * Stop camera
     */
    stopCamera() {
        if (this.video && this.video.srcObject) {
            const tracks = this.video.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            this.video.srcObject = null;
        }

        this.isActive = false;

        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }

        // Stop performance monitoring
        window.performanceMonitor.stop();

        // Update UI
        const startButton = document.getElementById('start-glasses-btn');
        if (startButton) {
            startButton.textContent = 'Start Camera';
            startButton.classList.remove('bg-red-500');
            startButton.classList.add('bg-blue-500');
        }

        console.log('Camera stopped');
    }

    /**
     * Main detection and rendering loop
     */
    async detectAndRender() {
        if (!this.isActive || !this.video || this.video.readyState !== 4) {
            if (this.isActive) {
                this.animationFrameId = requestAnimationFrame(() => this.detectAndRender());
            }
            return;
        }

        try {
            // Detect faces
            const predictions = await this.model.estimateFaces({ input: this.video });
            
            if (predictions.length > 0) {
                this.positionGlasses(predictions[0]);
                this.glassesMesh.visible = true;
            } else {
                this.glassesMesh.visible = false;
            }
            
            // Render Three.js scene
            this.renderer.render(this.scene, this.camera);
            
        } catch (error) {
            console.warn('Face detection error:', error);
        }
        
        if (this.isActive) {
            this.animationFrameId = requestAnimationFrame(() => this.detectAndRender());
        }
    }

    /**
     * Position glasses based on face landmarks
     */
    positionGlasses(face) {
        const keypoints = face.scaledMesh;
        
        // Get eye landmarks (approximate positions)
        const leftEye = keypoints[130];  // Left eye center
        const rightEye = keypoints[359]; // Right eye center
        const noseBridge = keypoints[168]; // Nose bridge
        
        if (!leftEye || !rightEye || !noseBridge) return;
        
        // Calculate eye center and distance
        const eyeCenterX = (leftEye[0] + rightEye[0]) / 2;
        const eyeCenterY = (leftEye[1] + rightEye[1]) / 2;
        const eyeDistance = Math.sqrt(
            Math.pow(rightEye[0] - leftEye[0], 2) + 
            Math.pow(rightEye[1] - leftEye[1], 2)
        );
        
        // Calculate rotation angle
        const angle = Math.atan2(rightEye[1] - leftEye[1], rightEye[0] - leftEye[0]);
        
        // Convert screen coordinates to Three.js coordinates
        const x = (eyeCenterX / this.video.videoWidth) * 2 - 1;
        const y = -((eyeCenterY / this.video.videoHeight) * 2 - 1);
        
        // Position glasses
        this.glassesMesh.position.x = x * 2;
        this.glassesMesh.position.y = y * 1.5;
        this.glassesMesh.position.z = 0;
        
        // Scale based on eye distance
        const scale = (eyeDistance / 100) * 1.5;
        this.glassesMesh.scale.set(scale, scale, 1);
        
        // Rotate glasses
        this.glassesMesh.rotation.z = angle;
    }

    /**
     * Setup glasses selection UI
     */
    setupGlassesSelection() {
        const container = document.getElementById('glasses-selection');
        if (!container) return;

        container.innerHTML = '';
        
        this.glassesItems.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'glasses-item';
            itemElement.innerHTML = `
                <img src="${item.preview}" alt="${item.name}" onerror="this.src='/static/images/placeholder.png'">
                <div class="name">${item.name}</div>
            `;
            
            itemElement.addEventListener('click', () => {
                this.selectGlasses(index);
            });
            
            container.appendChild(itemElement);
        });
        
        // Select first item by default
        this.selectGlasses(0);
    }

    /**
     * Select glasses item
     */
    async selectGlasses(index) {
        this.selectedGlassesIndex = index;
        
        // Update UI selection
        const items = document.querySelectorAll('.glasses-item');
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
        
        // Update glasses mesh
        await this.createGlassesMesh();
    }

    /**
     * Setup camera controls
     */
    setupCameraControls() {
        const startButton = document.getElementById('start-glasses-btn');
        if (startButton) {
            startButton.addEventListener('click', () => {
                if (this.isActive) {
                    this.stopCamera();
                } else {
                    this.startCamera();
                }
            });
        }
    }

    /**
     * Capture screenshot
     */
    captureScreenshot() {
        if (!this.video || !this.canvas) return;
        
        // Draw video frame to canvas
        this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
        
        // Get image data
        const imageData = this.canvas.toDataURL('image/png');
        
        // Create download link
        const link = document.createElement('a');
        link.download = 'glasses-tryon-' + Date.now() + '.png';
        link.href = imageData;
        link.click();
    }

    /**
     * Show error message
     */
    showError(message) {
        const errorContainer = document.getElementById('glasses-error-message');
        if (errorContainer) {
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
        }
        console.error('Glasses VTO Error:', message);
    }

    /**
     * Cleanup resources
     */
    dispose() {
        this.stopCamera();
        
        if (this.glassesMesh) {
            this.scene.remove(this.glassesMesh);
        }
        
        window.threeUtils.dispose('glasses-overlay');
    }
}

// Create global instance
window.glassesVTO = new GlassesVTO();
