# 🚀 VirtualTryOn Platform - Deployment Guide

## Overview

This guide covers deploying the VirtualTryOn platform with all AR features to production environments.

## Prerequisites

- HTTPS certificate (required for WebXR and camera access)
- Domain name
- Server with Python 3.8+ support
- Environment variables configured

## Environment Variables

Create a `.env` file in the `client-side` directory:

```bash
# Flask Configuration
FLASK_SECRET_KEY=your-super-secret-key-here
FLASK_ENV=production

# AI Services
GEMINI_API_KEY=your-gemini-api-key-here
AI_SERVER_URL=https://your-ai-server.com/api/transform
DEMO_MODE=false

# Security
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
```

## Deployment Options

### Option 1: Traditional Server Deployment

#### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv nginx -y

# Create application user
sudo useradd -m -s /bin/bash virtualtryonapp
sudo su - virtualtryonapp
```

#### 2. Application Setup
```bash
# Clone repository
git clone https://github.com/your-repo/VirtualTryOn.git
cd VirtualTryOn

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r client-side/requirements.txt
pip install gunicorn
```

#### 3. Gunicorn Configuration
Create `gunicorn.conf.py`:
```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

#### 4. Systemd Service
Create `/etc/systemd/system/virtualtryonapp.service`:
```ini
[Unit]
Description=VirtualTryOn Flask App
After=network.target

[Service]
User=virtualtryonapp
Group=virtualtryonapp
WorkingDirectory=/home/<USER>/VirtualTryOn/client-side
Environment=PATH=/home/<USER>/VirtualTryOn/venv/bin
ExecStart=/home/<USER>/VirtualTryOn/venv/bin/gunicorn -c gunicorn.conf.py app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

#### 5. Nginx Configuration
Create `/etc/nginx/sites-available/virtualtryonapp`:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Security headers for AR features
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Required for WebXR
    add_header Feature-Policy "camera 'self'; microphone 'none'; geolocation 'none'";
    add_header Permissions-Policy "camera=(self), microphone=(), geolocation=()";

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for potential future features
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Static files optimization
    location /static/ {
        alias /home/<USER>/VirtualTryOn/client-side/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Compress 3D models and textures
        location ~* \.(glb|gltf)$ {
            gzip on;
            gzip_types application/octet-stream;
        }
    }

    # Security for model files
    location /static/models/ {
        alias /home/<USER>/VirtualTryOn/client-side/static/models/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
    }
}
```

#### 6. Enable and Start Services
```bash
# Enable Nginx site
sudo ln -s /etc/nginx/sites-available/virtualtryonapp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# Enable and start application
sudo systemctl enable virtualtryonapp
sudo systemctl start virtualtryonapp
sudo systemctl status virtualtryonapp
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY client-side/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn

# Copy application
COPY client-side/ .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "app:app"]
```

#### 2. Docker Compose
```yaml
version: '3.8'

services:
  virtualtryonapp:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - AI_SERVER_URL=${AI_SERVER_URL}
      - DEMO_MODE=false
    volumes:
      - ./client-side/static/images:/app/static/images
      - ./client-side/static/models:/app/static/models
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - virtualtryonapp
    restart: unless-stopped
```

### Option 3: Cloud Platform Deployment

#### Heroku
```bash
# Install Heroku CLI and login
heroku login

# Create app
heroku create your-virtualtryonapp

# Set environment variables
heroku config:set FLASK_ENV=production
heroku config:set GEMINI_API_KEY=your-key
heroku config:set AI_SERVER_URL=your-ai-server-url

# Deploy
git push heroku main
```

#### Vercel/Netlify
For static hosting with serverless functions, you'll need to adapt the Flask app to serverless architecture.

## Performance Optimization

### 1. 3D Model Optimization
```bash
# Compress GLB files
npx gltf-pipeline -i input.glb -o output.glb --draco.compressionLevel=10

# Optimize textures
npx imagemin static/images/glasses/*.png --out-dir=static/images/glasses/optimized
```

### 2. CDN Configuration
- Use CDN for static assets (models, textures, images)
- Enable gzip compression for all text-based files
- Set appropriate cache headers

### 3. Database Optimization (if using database)
- Index frequently queried fields
- Use connection pooling
- Implement caching layer (Redis)

## Monitoring and Logging

### 1. Application Monitoring
```python
# Add to app.py
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/virtualtryonapp.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

### 2. Performance Monitoring
- Monitor WebXR session success rates
- Track face detection accuracy
- Monitor 3D model loading times
- Set up alerts for high error rates

## Security Considerations

### 1. HTTPS Requirements
- WebXR requires HTTPS in production
- Camera access requires HTTPS
- Use strong SSL/TLS configuration

### 2. Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
    style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com;
    img-src 'self' data: blob:;
    media-src 'self' blob:;
    connect-src 'self' https://api.gemini.com;
    worker-src 'self' blob:;
">
```

### 3. Rate Limiting
```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/api/furniture-catalog')
@limiter.limit("10 per minute")
def get_furniture_catalog():
    # ... existing code
```

## Troubleshooting

### Common Deployment Issues

1. **WebXR not working in production**
   - Ensure HTTPS is properly configured
   - Check browser compatibility
   - Verify WebXR permissions headers

2. **Camera access denied**
   - Confirm HTTPS certificate is valid
   - Check permissions policy headers
   - Verify domain is not blocked

3. **3D models not loading**
   - Check CORS headers for model files
   - Verify file paths and permissions
   - Monitor network requests in browser dev tools

4. **High memory usage**
   - Implement model caching strategies
   - Use model compression
   - Monitor Three.js memory usage

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor SSL certificate expiration
- Review application logs weekly
- Test AR features on different devices
- Update 3D model catalog as needed

### Backup Strategy
- Backup uploaded images and models
- Backup configuration files
- Document deployment procedures
- Test restore procedures regularly

---

For additional support, refer to the main README.md or create an issue in the repository.
