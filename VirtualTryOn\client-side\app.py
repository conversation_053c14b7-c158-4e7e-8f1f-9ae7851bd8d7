from flask import Flask, request, jsonify, render_template, redirect, url_for, flash
from PIL import Image
import requests
from io import BytesIO
import base64
import os
import logging
import glob
import io
from dotenv import load_dotenv
import google.generativeai as genai
from gemini_placer import <PERSON><PERSON><PERSON><PERSON>
from eye_detector import EyeDetector
from rembg import remove
import uuid
import re

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'a-fallback-secret-key')

# Configuration
UPLOAD_FOLDER = 'static/images/objects'
OUTPUT_FOLDER = 'static/output'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# AI Server Configuration
AI_SERVER_URL = os.getenv('AI_SERVER_URL', "http://localhost:8000/api/transform")
DEMO_MODE = os.getenv('DEMO_MODE', 'true').lower() == 'true'

def download_model(url, filename):
    """Downloads a model file if it doesn't exist."""
    if not os.path.exists(filename):
        logger.info(f"Downloading {filename} from {url}...")
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"Successfully downloaded {filename}.")
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download {filename}: {e}")
            raise

# Create models directory and download cascades
models_dir = os.path.join(os.path.dirname(__file__), 'models')
os.makedirs(models_dir, exist_ok=True)

face_cascade_url = "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml"
eye_cascade_url = "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_eye.xml"
face_cascade_path = os.path.join(models_dir, "haarcascade_frontalface_default.xml")
eye_cascade_path = os.path.join(models_dir, "haarcascade_eye.xml")

try:
    download_model(face_cascade_url, face_cascade_path)
    download_model(eye_cascade_url, eye_cascade_path)
    eye_detector = EyeDetector(face_cascade_path, eye_cascade_path)
    logger.info("Eye detector initialized successfully.")
except Exception as e:
    eye_detector = None
    logger.error(f"Failed to initialize EyeDetector: {e}. Eyeglass placement will be disabled.")


# Initialize Gemini Model and Placer
gemini_placer_instance = None
try:
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if gemini_api_key:
        genai.configure(api_key=gemini_api_key)
        model = genai.GenerativeModel('gemini-2.5-pro')
        gemini_placer_instance = GeminiPlacer(model)
        logger.info("Gemini Placer initialized successfully.")
    else:
        logger.warning("GEMINI_API_KEY not found. Object placement feature will be disabled.")
except Exception as e:
    logger.error(f"Failed to initialize Gemini Placer: {e}")

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_cloth_products():
    """Helper function to get cloth products"""
    cloth_images = []
    image_folder = os.path.join(app.static_folder, 'images')
    
    # Get all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif']
    for ext in image_extensions:
        cloth_images.extend(glob.glob(os.path.join(image_folder, ext)))
    
    # Convert to relative URLs and create product data
    cloth_products = []
    for i, image_path in enumerate(cloth_images):
        filename = os.path.basename(image_path)
        relative_url = f"images/{filename}"
        
        # Create a product name from filename
        name = filename.replace('_', ' ').replace('.', ' ').title()
        if name.startswith('0'):
            name = name[1:]  # Remove leading zero
        
        cloth_products.append({
            'id': i + 1,
            'name': name,
            'image': relative_url,
            'category': 'clothes',
            'rating': 4.5 + (i % 5) * 0.1,  # Random rating between 4.5-5.0
            'store': 'Fashion Collection',
            'price': 29.99 + (i * 10)  # Varying prices
        })
    
    return cloth_products

def get_object_products():
    """Helper function to get object products"""
    object_images = []
    image_folder = os.path.join(app.static_folder, 'images/objects')
    
    # Create directory if it doesn't exist
    os.makedirs(image_folder, exist_ok=True)

    # Get all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif']
    for ext in image_extensions:
        object_images.extend(glob.glob(os.path.join(image_folder, ext)))
    
    # Convert to relative URLs and create product data
    object_products = []
    for i, image_path in enumerate(object_images):
        filename = os.path.basename(image_path)
        relative_url = f"images/objects/{filename}"
        
        # Create a product name from filename
        name = filename.split('.')[0].replace('_', ' ').title()
        
        object_products.append({
            'id': i + 1,
            'name': name,
            'image': relative_url,
            'category': 'objects',
        })
    
    return object_products

def get_furniture_products():
    """Helper function to get furniture products"""
    furniture_items = [
        {
            'id': 'armchair_yellow',
            'name': 'Dylan Armchair',
            'model': '/static/models/furniture/dylan_armchair_yolk_yellow.glb',
            'preview': '/static/images/furniture/armchair.png',
            'category': 'furniture',
            'price': 899.99,
            'rating': 4.8
        },
        {
            'id': 'armchair_blue',
            'name': 'Ivan Armchair',
            'model': '/static/models/furniture/ivan_armchair_mineral_blue.glb',
            'preview': '/static/images/furniture/lounger.png',
            'category': 'furniture',
            'price': 1299.99,
            'rating': 4.9
        },
        {
            'id': 'coffee_table_marble',
            'name': 'Marble Coffee Table',
            'model': '/static/models/furniture/marble_coffee_table.glb',
            'preview': '/static/images/furniture/marble-coffeetable.png',
            'category': 'furniture',
            'price': 599.99,
            'rating': 4.7
        },
        {
            'id': 'coffee_table_walnut',
            'name': 'Walnut Coffee Table',
            'model': '/static/models/furniture/flippa_functional_coffee_table_w._storagewalnut.glb',
            'preview': '/static/images/furniture/walnut-coffeetable.png',
            'category': 'furniture',
            'price': 749.99,
            'rating': 4.6
        },
        {
            'id': 'chair_gold',
            'name': 'Gold Frame Chair',
            'model': '/static/models/furniture/frame_armchairpetrol_velvet_with_gold_frame.glb',
            'preview': '/static/images/furniture/chair-with-gold.png',
            'category': 'furniture',
            'price': 1099.99,
            'rating': 4.8
        },
        {
            'id': 'nesting_tables',
            'name': 'Nesting Tables',
            'model': '/static/models/furniture/elnaz_nesting_side_tables_brass__green_marble.glb',
            'preview': '/static/images/furniture/nesting-tables.png',
            'category': 'furniture',
            'price': 449.99,
            'rating': 4.5
        },
        {
            'id': 'air_conditioner',
            'name': 'Air Conditioner',
            'model': '/static/models/furniture/air_conditioner.glb',
            'preview': '/static/images/furniture/air_conditioner.png',
            'category': 'furniture',
            'price': 799.99,
            'rating': 4.4
        },
        {
            'id': 'table_lamp',
            'name': 'Table Lamp',
            'model': '/static/models/furniture/table_lamp.glb',
            'preview': '/static/images/furniture/table_lamp.png',
            'category': 'furniture',
            'price': 129.99,
            'rating': 4.6
        }
    ]
    return furniture_items

def get_glasses_products():
    """Helper function to get glasses products"""
    glasses_items = [
        {
            'id': 'sunglasses_classic',
            'name': 'Classic Sunglasses',
            'texture': '/static/images/glasses/sunglasses_classic.png',
            'preview': '/static/images/glasses/sunglasses_classic_preview.png',
            'category': 'sunglasses',
            'price': 149.99,
            'rating': 4.7
        },
        {
            'id': 'sunglasses_aviator',
            'name': 'Aviator Sunglasses',
            'texture': '/static/images/glasses/sunglasses.png',
            'preview': '/static/images/glasses/sunglasses.png',
            'category': 'sunglasses',
            'price': 199.99,
            'rating': 4.8
        },
        {
            'id': 'eyeglasses_round',
            'name': 'Round Eyeglasses',
            'texture': '/static/images/glasses/eyeglasses_round.png',
            'preview': '/static/images/glasses/eyeglasses_round_preview.png',
            'category': 'eyeglasses',
            'price': 249.99,
            'rating': 4.6
        },
        {
            'id': 'eyeglasses_square',
            'name': 'Square Eyeglasses',
            'texture': '/static/images/glasses/glasses.png',
            'preview': '/static/images/glasses/glasses.png',
            'category': 'eyeglasses',
            'price': 279.99,
            'rating': 4.5
        }
    ]
    return glasses_items

@app.route('/')
def home():
    cloth_products = get_cloth_products()
    object_products = get_object_products()
    furniture_products = get_furniture_products()
    glasses_products = get_glasses_products()
    return render_template("index.html",
                         cloth_products=cloth_products,
                         object_products=object_products,
                         furniture_products=furniture_products,
                         glasses_products=glasses_products)

@app.route("/preds", methods=['POST'])
def submit():
    try:
        # Check if model file is present
        if 'model' not in request.files:
            error_msg = 'Please select a model image'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': error_msg}), 400
            flash(error_msg, 'error')
            return redirect(url_for('home'))
        
        model = request.files['model']
        
        # Check if model file is selected
        if model.filename == '':
            error_msg = 'Please select a model image'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': error_msg}), 400
            flash(error_msg, 'error')
            return redirect(url_for('home'))
        
        # Validate model file type
        if not allowed_file(model.filename):
            error_msg = 'Please upload only PNG, JPG, JPEG, or GIF files for model image'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': error_msg}), 400
            flash(error_msg, 'error')
            return redirect(url_for('home'))
        
        # Get cloth image path from form data
        cloth_image_path = request.form.get('cloth_image')
        if not cloth_image_path:
            error_msg = 'Please select a cloth image'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': error_msg}), 400
            flash(error_msg, 'error')
            return redirect(url_for('home'))
        
        # Construct full path to cloth image
        cloth_image_full_path = os.path.join(app.static_folder, cloth_image_path)
        if not os.path.exists(cloth_image_full_path):
            error_msg = 'Selected cloth image not found'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': error_msg}), 400
            flash(error_msg, 'error')
            return redirect(url_for('home'))
        
        logger.info("Processing virtual try-on request")
        
        if DEMO_MODE:
            # Demo mode: just return the model image as result
            logger.info("Running in demo mode - returning model image")
            model_img = Image.open(model.stream)
            
            buffer = BytesIO()
            model_img.save(buffer, 'PNG')
            buffer.seek(0)
            
            data = buffer.read()
            data = base64.b64encode(data).decode()
            
            logger.info(f"Demo mode: Generated base64 data length: {len(data)}")
            
            # Check if this is an AJAX request
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'demo_mode': True,
                    'image_data': data,
                    'message': 'Demo mode: This is just the original model image. Set up AI server for real virtual try-on.'
                })
            else:
                flash('Demo mode: This is just the original model image. Set up AI server for real virtual try-on.', 'info')
                # Get cloth products for template
                cloth_products = get_cloth_products()
                return render_template('index.html', op=data, success=True, demo_mode=True, cloth_products=cloth_products)
        
        # Production mode: Make request to AI server
        try:
            # Open cloth image from file
            with open(cloth_image_full_path, 'rb') as cloth_file:
                response = requests.post(
                    url=AI_SERVER_URL, 
                    files={"cloth": cloth_file, "model": model.stream},
                    timeout=60
                )
            
            if response.status_code == 200:
                # Process the response
                op = Image.open(BytesIO(response.content))
                
                buffer = BytesIO()
                op.save(buffer, 'PNG')
                buffer.seek(0)
                
                data = buffer.read()
                data = base64.b64encode(data).decode()
                
                logger.info("Virtual try-on completed successfully")
                
                # Check if this is an AJAX request
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': True,
                        'demo_mode': False,
                        'image_data': data,
                        'message': 'Virtual try-on completed successfully!'
                    })
                else:
                    # Get cloth products for template
                    cloth_products = get_cloth_products()
                    return render_template('index.html', op=data, success=True, cloth_products=cloth_products)
            else:
                logger.error(f"AI server request failed with status: {response.status_code}")
                error_msg = 'Virtual try-on service returned an error. Please try again.'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'message': error_msg}), 500
                flash(error_msg, 'error')
                return redirect(url_for('home'))
                
        except requests.exceptions.RequestException as e:
            logger.error(f"AI server connection error: {str(e)}")
            error_msg = f'Cannot connect to AI server at {AI_SERVER_URL}. Please check if the server is running.'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': error_msg}), 500
            flash(error_msg, 'error')
            return redirect(url_for('home'))
    
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        error_msg = 'An unexpected error occurred. Please try again.'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': error_msg}), 500
        flash(error_msg, 'error')
        return redirect(url_for('home'))

@app.route('/place-object', methods=['POST'])
def place_object():
    if not gemini_placer_instance:
        return jsonify({'success': False, 'message': 'Object placement feature is not configured. Check GEMINI_API_KEY.'}), 500

    try:
        # Check if background_image file is present
        if 'background_image' not in request.files:
            return jsonify({'success': False, 'message': 'Please select a background image'}), 400
        
        background_image_file = request.files['background_image']
        
        if background_image_file.filename == '':
            return jsonify({'success': False, 'message': 'Please select a background image'}), 400

        if not allowed_file(background_image_file.filename):
            return jsonify({'success': False, 'message': 'Please upload only PNG, JPG, JPEG, or GIF files'}), 400

        object_image_path = request.form.get('object_image')
        if not object_image_path:
            return jsonify({'success': False, 'message': 'Object image path not provided'}), 400
            
        object_image_full_path = os.path.join(app.static_folder, object_image_path)
        if not os.path.exists(object_image_full_path):
            return jsonify({'success': False, 'message': f'Object image not found at {object_image_full_path}'}), 400

        # Get the dimensions of the object image
        with Image.open(object_image_full_path) as img:
            object_dimensions = img.size  # (width, height)

        # Extract object name from path for the prompt
        object_name = os.path.basename(object_image_path).split('.')[0].replace('_', ' ').lower()
        
        # Generate contextual hint based on object name
        context_hint = ""
        if "necklace" in object_name:
            context_hint = "This is a necklace. It should be placed on a person's neck."
        elif "earring" in object_name:
            context_hint = "These are earrings. They should be placed on a person's ears."
        elif "watch" in object_name:
            context_hint = "This is a watch. It should be placed on a person's wrist."
        elif "lamp" in object_name:
            context_hint = "This is a lamp. It should be placed on a flat surface like a table, desk, or floor, not floating in the air."
        elif "tv" in object_name or "television" in object_name:
            context_hint = "This is a TV. It should be mounted on a prominent blank wall or placed on a media console."
        elif "sofa" in object_name or "couch" in object_name:
            context_hint = "This is a sofa. It should be placed in a living room area, against a wall or in the center of the room."
        elif "chair" in object_name:
            context_hint = "This is a chair. It could be an office chair, dining chair, or armchair. Place it appropriately based on the room's context."
        elif "plant" in object_name:
            context_hint = "This is a potted plant. It should be placed on the floor, a table, or a shelf."
        elif "eye glasses" in object_name:
            context_hint = "This is a pair of eye glasses. It should be placed on a person's face. The glasses should be placed on the nose and the ears should be free."
        elif "sun glasses" in object_name:
            context_hint = "This is a pair of sun glasses. It should be placed on a person's face. The glasses should be placed on the nose and the ears should be free."

        background_image_bytes = background_image_file.read()

        # Get placement from Gemini
        logger.info(f"Requesting object placement for '{object_name}' from Gemini with hint: '{context_hint}'")
        placement = gemini_placer_instance.get_object_placement(
            background_image_bytes, 
            object_name, 
            context_hint,
            object_dimensions=object_dimensions
        )
        logger.info(f"Received initial placement from Gemini: {placement}")
        
        # --- Custom logic for eyeglasses ---
        if ("eye glasses" in object_name or "sun glasses" in object_name) and eye_detector:
            logger.info("Eyeglasses detected, running custom eye detection.")
            eye_data = eye_detector.detect_eyes(background_image_bytes)
            if eye_data:
                logger.info(f"Eye detection successful: {eye_data}")
                # Override position and rotation with precise values
                placement['x'] = eye_data['eye_center'][0]
                placement['y'] = eye_data['eye_center'][1]
                placement['rotation'] = eye_data['angle']
                logger.info(f"Overriding placement with x: {placement['x']}, y: {placement['y']}, rotation: {placement['rotation']:.2f}")
            else:
                logger.warning("Eye detection failed, falling back to standard placement.")
        # --- End custom logic ---

        # Overlay the object
        final_image = gemini_placer_instance.overlay_object(background_image_bytes, object_image_full_path, placement)

        # Prepare image for response
        buffer = BytesIO()
        final_image.save(buffer, 'JPEG')
        buffer.seek(0)
        
        data = base64.b64encode(buffer.read()).decode()
        
        return jsonify({
            'success': True,
            'image_data': data,
            'message': 'Object placed successfully!'
        })

    except Exception as e:
        logger.error(f"Error in /place-object: {str(e)}")
        return jsonify({'success': False, 'message': 'An unexpected error occurred during object placement.'}), 500

def process_image_from_url(image_url, product_name="custom_object"):
    """Downloads an image, removes background, and saves it temporarily."""
    try:
        response = requests.get(image_url, stream=True, timeout=20)
        response.raise_for_status()

        input_bytes = response.content
        output_bytes = remove(input_bytes)

        # Sanitize product name to create a safe filename
        safe_filename = re.sub(r'[^a-zA-Z0-9_-]', '_', product_name).lower()
        temp_filename = f"{safe_filename}_{uuid.uuid4().hex[:6]}.png"

        # Save the processed image to the UPLOAD_FOLDER
        save_path = os.path.join(app.config['UPLOAD_FOLDER'], temp_filename)
        
        with open(save_path, 'wb') as f:
            f.write(output_bytes)
            
        # Return the relative path for web access
        return os.path.join('images/objects', temp_filename)

    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download image from {image_url}: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to process image with rembg: {e}")
        return None

@app.route('/process-image-from-url', methods=['POST'])
def process_url_endpoint():
    """API endpoint to process an image from a URL."""
    data = request.get_json()
    if not data or 'image_url' not in data:
        return jsonify({'success': False, 'message': 'Image URL is required.'}), 400
        
    image_url = data['image_url']
    product_name = data.get('product_name', 'custom_object') # Use a default if not provided
    
    logger.info(f"Processing image for '{product_name}' from URL: {image_url}")
    
    processed_path = process_image_from_url(image_url, product_name)
    
    if processed_path:
        return jsonify({'success': True, 'object_path': processed_path})
    else:
        return jsonify({'success': False, 'message': 'Failed to process image from URL. Please check the link or try a different image.'}), 500


@app.route('/api/products')
def get_products():
    """API endpoint to get sample products"""
    sample_products = [
        {
            'id': 1,
            'name': 'Classic Denim Jacket',
            'price': 89.99,
            'image': 'https://images.pexels.com/photos/1183266/pexels-photo-1183266.jpeg?auto=compress&cs=tinysrgb&w=400',
            'category': 'clothes',
            'rating': 4.8,
            'store': 'Fashion Hub'
        },
        {
            'id': 2,
            'name': 'Diamond Necklace',
            'price': 299.99,
            'image': 'https://images.pexels.com/photos/1927259/pexels-photo-1927259.jpeg?auto=compress&cs=tinysrgb&w=400',
            'category': 'jewelry',
            'rating': 4.9,
            'store': 'Luxury Jewelry'
        },
        {
            'id': 3,
            'name': 'Red Lipstick',
            'price': 24.99,
            'image': 'https://images.pexels.com/photos/2533266/pexels-photo-2533266.jpeg?auto=compress&cs=tinysrgb&w=400',
            'category': 'beauty',
            'rating': 4.8,
            'store': 'Beauty Store'
        },
        {
            'id': 4,
            'name': 'Gold Earrings',
            'price': 159.99,
            'image': 'https://images.pexels.com/photos/1927260/pexels-photo-1927260.jpeg?auto=compress&cs=tinysrgb&w=400',
            'category': 'jewelry',
            'rating': 4.7,
            'store': 'Gold Collection'
        },
        {
            'id': 5,
            'name': 'Casual T-Shirt',
            'price': 29.99,
            'image': 'https://images.pexels.com/photos/2294342/pexels-photo-2294342.jpeg?auto=compress&cs=tinysrgb&w=400',
            'category': 'clothes',
            'rating': 4.5,
            'store': 'Casual Wear'
        },
        {
            'id': 6,
            'name': 'Foundation Makeup',
            'price': 39.99,
            'image': 'https://images.pexels.com/photos/2533265/pexels-photo-2533265.jpeg?auto=compress&cs=tinysrgb&w=400',
            'category': 'beauty',
            'rating': 4.6,
            'store': 'Makeup Pro'
        }
    ]
    return jsonify(sample_products)

@app.route('/api/cloth-images')
def get_cloth_images():
    """API endpoint to get available cloth images"""
    cloth_products = get_cloth_products()
    return jsonify(cloth_products)

@app.route('/api/furniture-catalog')
def get_furniture_catalog():
    """API endpoint to get furniture catalog"""
    furniture_products = get_furniture_products()
    return jsonify(furniture_products)

@app.route('/api/glasses-catalog')
def get_glasses_catalog():
    """API endpoint to get glasses catalog"""
    glasses_products = get_glasses_products()
    return jsonify(glasses_products)

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'TryOn Vibe API is running'})

@app.route('/api/ar-support')
def check_ar_support():
    """Check AR feature support"""
    # This is a server-side check, actual WebXR support is checked client-side
    return jsonify({
        'webxr_available': True,  # Assume available, real check happens client-side
        'tensorflow_available': True,  # TensorFlow.js loaded via CDN
        'camera_required': True,
        'https_required': True,
        'supported_browsers': ['Chrome on Android', 'Safari on iOS', 'Chrome on Desktop (with flags)']
    })

@app.errorhandler(404)
def page_not_found(e):
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_server_error(e):
    logger.error(f"Internal server error: {str(e)}")
    return render_template('index.html'), 500

if __name__ == '__main__':
    print("=" * 50)
    print("🚀 TryOn Vibe - Virtual Try-On Platform")
    print("=" * 50)
    if DEMO_MODE:
        print("🎭 Running in DEMO MODE")
        print("   - Virtual try-on will just return the model image")
        print("   - To use real AI, set up the AI server and update AI_SERVER_URL")
    else:
        print(f"🤖 AI Server URL: {AI_SERVER_URL}")
        print("   - Make sure your AI server is running at this URL")
    
    print("🌐 Starting Flask app on http://localhost:5001")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5001)
