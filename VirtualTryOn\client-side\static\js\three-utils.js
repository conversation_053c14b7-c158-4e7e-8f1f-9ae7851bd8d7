/**
 * Three.js Utilities for VirtualTryOn AR Features
 * Shared utilities for 3D rendering, scene management, and AR functionality
 */

class ThreeUtils {
    constructor() {
        this.scenes = new Map();
        this.renderers = new Map();
        this.cameras = new Map();
        this.loaders = {
            gltf: null,
            texture: new THREE.TextureLoader()
        };
        this.initLoaders();
    }

    /**
     * Initialize loaders when available
     */
    initLoaders() {
        // Initialize GLTF loader when available
        if (typeof THREE.GLTFLoader !== 'undefined') {
            this.loaders.gltf = new THREE.GLTFLoader();
        } else {
            // Wait for GLTFLoader to be available
            const checkLoader = () => {
                if (typeof THREE.GLTFLoader !== 'undefined') {
                    this.loaders.gltf = new THREE.GLTFLoader();
                } else {
                    setTimeout(checkLoader, 100);
                }
            };
            checkLoader();
        }
    }

    /**
     * Create a basic Three.js scene with camera and renderer
     */
    createScene(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`Container with id '${containerId}' not found`);
        }

        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(
            options.fov || 75,
            container.clientWidth / container.clientHeight,
            options.near || 0.1,
            options.far || 1000
        );

        const renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: options.alpha !== false
        });
        
        renderer.setSize(container.clientWidth, container.clientHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        
        if (options.shadowMap) {
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        }

        container.appendChild(renderer.domElement);

        // Store references
        this.scenes.set(containerId, scene);
        this.renderers.set(containerId, renderer);
        this.cameras.set(containerId, camera);

        // Add basic lighting if requested
        if (options.lighting !== false) {
            this.addBasicLighting(scene);
        }

        return { scene, camera, renderer };
    }

    /**
     * Add basic lighting to a scene
     */
    addBasicLighting(scene) {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        scene.add(directionalLight);
    }

    /**
     * Load a GLTF model
     */
    async loadGLTFModel(url) {
        if (!this.loaders.gltf) {
            throw new Error('GLTF Loader not available');
        }

        return new Promise((resolve, reject) => {
            this.loaders.gltf.load(
                url,
                (gltf) => resolve(gltf),
                (progress) => console.log('Loading progress:', progress),
                (error) => reject(error)
            );
        });
    }

    /**
     * Load a texture
     */
    async loadTexture(url) {
        return new Promise((resolve, reject) => {
            this.loaders.texture.load(
                url,
                (texture) => resolve(texture),
                (progress) => console.log('Loading progress:', progress),
                (error) => reject(error)
            );
        });
    }

    /**
     * Resize renderer and camera when container size changes
     */
    handleResize(containerId) {
        const container = document.getElementById(containerId);
        const camera = this.cameras.get(containerId);
        const renderer = this.renderers.get(containerId);

        if (container && camera && renderer) {
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        }
    }

    /**
     * Clean up Three.js resources
     */
    dispose(containerId) {
        const scene = this.scenes.get(containerId);
        const renderer = this.renderers.get(containerId);

        if (scene) {
            // Dispose of all objects in the scene
            scene.traverse((object) => {
                if (object.geometry) object.geometry.dispose();
                if (object.material) {
                    if (Array.isArray(object.material)) {
                        object.material.forEach(material => material.dispose());
                    } else {
                        object.material.dispose();
                    }
                }
            });
        }

        if (renderer) {
            renderer.dispose();
            if (renderer.domElement && renderer.domElement.parentNode) {
                renderer.domElement.parentNode.removeChild(renderer.domElement);
            }
        }

        // Remove from maps
        this.scenes.delete(containerId);
        this.renderers.delete(containerId);
        this.cameras.delete(containerId);
    }

    /**
     * Check WebXR support
     */
    async checkWebXRSupport() {
        if (!navigator.xr) {
            return { supported: false, reason: 'WebXR not available' };
        }

        try {
            const supported = await navigator.xr.isSessionSupported('immersive-ar');
            return { 
                supported, 
                reason: supported ? null : 'AR sessions not supported' 
            };
        } catch (error) {
            return { 
                supported: false, 
                reason: 'Error checking WebXR support: ' + error.message 
            };
        }
    }

    /**
     * Check camera access
     */
    async checkCameraAccess() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ video: true });
            // Stop the stream immediately after checking
            stream.getTracks().forEach(track => track.stop());
            return { supported: true, reason: null };
        } catch (error) {
            return { 
                supported: false, 
                reason: 'Camera access denied or not available: ' + error.message 
            };
        }
    }

    /**
     * Create a reticle for AR placement
     */
    createReticle() {
        const reticle = new THREE.Mesh(
            new THREE.RingGeometry(0.15, 0.2, 32).rotateX(-Math.PI / 2),
            new THREE.MeshBasicMaterial({ color: 0xffffff, opacity: 0.8, transparent: true })
        );
        reticle.matrixAutoUpdate = false;
        reticle.visible = false;
        return reticle;
    }

    /**
     * Utility to convert screen coordinates to world coordinates
     */
    screenToWorld(x, y, camera, distance = 10) {
        const vector = new THREE.Vector3();
        vector.set(
            (x / window.innerWidth) * 2 - 1,
            -(y / window.innerHeight) * 2 + 1,
            0.5
        );
        vector.unproject(camera);
        
        const dir = vector.sub(camera.position).normalize();
        const pos = camera.position.clone().add(dir.multiplyScalar(distance));
        return pos;
    }

    /**
     * Calculate distance between two 3D points
     */
    calculateDistance(point1, point2) {
        return point1.distanceTo(point2);
    }

    /**
     * Animate object properties using simple easing
     */
    animateObject(object, targetProperties, duration = 1000, easing = 'easeInOut') {
        const startTime = Date.now();
        const startProperties = {};
        
        // Store initial values
        Object.keys(targetProperties).forEach(key => {
            if (key === 'position' || key === 'rotation' || key === 'scale') {
                startProperties[key] = object[key].clone();
            } else {
                startProperties[key] = object[key];
            }
        });

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Apply easing
            let easedProgress = progress;
            if (easing === 'easeInOut') {
                easedProgress = progress < 0.5 
                    ? 2 * progress * progress 
                    : 1 - Math.pow(-2 * progress + 2, 3) / 2;
            }

            // Interpolate properties
            Object.keys(targetProperties).forEach(key => {
                if (key === 'position' || key === 'rotation' || key === 'scale') {
                    object[key].lerpVectors(startProperties[key], targetProperties[key], easedProgress);
                } else {
                    object[key] = startProperties[key] + (targetProperties[key] - startProperties[key]) * easedProgress;
                }
            });

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }
}

// Create global instance
window.threeUtils = new ThreeUtils();

// Handle window resize for all scenes
window.addEventListener('resize', () => {
    window.threeUtils.scenes.forEach((scene, containerId) => {
        window.threeUtils.handleResize(containerId);
    });
});
