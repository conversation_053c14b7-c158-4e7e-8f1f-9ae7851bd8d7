# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class MapType(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = MapType()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsMapType(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def MapTypeBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x52\x54\x4D", size_prefixed=size_prefixed)

    # MapType
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # MapType
    def KeyType(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # MapType
    def ValueType(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from ort_flatbuffers_py.fbs.TypeInfo import TypeInfo
            obj = TypeInfo()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def MapTypeStart(builder):
    builder.StartObject(2)

def Start(builder):
    MapTypeStart(builder)

def MapTypeAddKeyType(builder, keyType):
    builder.PrependInt32Slot(0, keyType, 0)

def AddKeyType(builder, keyType):
    MapTypeAddKeyType(builder, keyType)

def MapTypeAddValueType(builder, valueType):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(valueType), 0)

def AddValueType(builder, valueType):
    MapTypeAddValueType(builder, valueType)

def MapTypeEnd(builder):
    return builder.EndObject()

def End(builder):
    return MapTypeEnd(builder)
