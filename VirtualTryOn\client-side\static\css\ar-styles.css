/* AR-specific styles for VirtualTryOn */

/* AR Furniture Styles */
.ar-container {
    position: relative;
    width: 100%;
    height: 500px;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
}

.ar-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.ar-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.ar-button {
    background: rgba(59, 130, 246, 0.9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.ar-button:hover {
    background: rgba(59, 130, 246, 1);
    transform: translateY(-2px);
}

.ar-button:disabled {
    background: rgba(107, 114, 128, 0.5);
    cursor: not-allowed;
    transform: none;
}

/* Furniture Selection */
.furniture-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.furniture-item {
    background: white;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.furniture-item:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.furniture-item.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}

.furniture-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 8px;
}

.furniture-item .name {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
}

/* Glasses Try-On Styles */
.glasses-container {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.webcam-container {
    position: relative;
    width: 100%;
    height: 600px;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
}

.webcam-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scaleX(-1); /* Mirror effect */
}

.glasses-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.glasses-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 10;
}

.glasses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.glasses-item {
    background: white;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.glasses-item:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.glasses-item.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}

.glasses-item img {
    width: 120px;
    height: 60px;
    object-fit: contain;
    border-radius: 8px;
    margin-bottom: 8px;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    border-radius: 12px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    margin-top: 15px;
    font-weight: 600;
}

/* Error States */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: center;
}

/* Feature Detection Messages */
.feature-warning {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    color: #d97706;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ar-container,
    .webcam-container {
        height: 400px;
    }
    
    .furniture-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .furniture-item img {
        width: 60px;
        height: 60px;
    }
    
    .glasses-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .glasses-item img {
        width: 100px;
        height: 50px;
    }
    
    .ar-controls,
    .glasses-controls {
        position: relative;
        bottom: auto;
        top: auto;
        right: auto;
        left: auto;
        transform: none;
        margin-top: 15px;
        justify-content: center;
    }
}

/* Tab-specific styles */
.tab-content.ar-furniture,
.tab-content.glasses-tryon {
    padding: 20px 0;
}

/* Hide elements when not supported */
.webxr-not-supported .ar-furniture-section {
    display: none;
}

.camera-not-supported .glasses-tryon-section {
    display: none;
}
