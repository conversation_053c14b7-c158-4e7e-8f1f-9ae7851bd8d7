<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryOn Vibe - Virtual Try-On Platform</title>
    <meta name="description" content="Experience the future of shopping with AR try-on technology. Try products before you buy, share with friends, and make confident purchase decisions.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- Three.js for 3D rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r153/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.153.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.153.0/examples/js/webxr/ARButton.js"></script>

    <!-- WebXR Polyfill for AR support -->
    <script src="https://cdn.jsdelivr.net/npm/webxr-polyfill@latest/build/webxr-polyfill.js"></script>

    <!-- TensorFlow.js for face detection -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-core@3.6.0/dist/tf-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-converter@3.6.0/dist/tf-converter.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-webgl@3.6.0/dist/tf-backend-webgl.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/face-landmarks-detection@0.0.3/dist/face-landmarks-detection.min.js"></script>

    <!-- AR-specific styles -->
    <link href="{{ url_for('static', filename='css/ar-styles.css') }}" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
        }
        .product-card {
            transition: all 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .cloth-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .cloth-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .cloth-card.selected {
            border: 3px solid #3b82f6;
            transform: translateY(-4px);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .file-upload-area {
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        .loading {
            display: none;
        }
        .loading.active {
            display: flex;
        }
    </style>
</head>

<body class="gradient-bg min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-eye text-white text-xl"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800">TryOn Vibe</h1>
                    </div>
                </div>
                
                <nav class="hidden md:flex space-x-8">
                    <button onclick="setActiveTab('clothes')" id="clothes-tab" class="text-blue-600 hover:text-blue-600 transition-colors">
                        Clothes
                    </button>
                    <button onclick="setActiveTab('ar-furniture')" id="ar-furniture-tab" class="text-gray-600 hover:text-blue-600 transition-colors">
                        AR Furniture
                    </button>
                    <button onclick="setActiveTab('glasses-tryon')" id="glasses-tryon-tab" class="text-gray-600 hover:text-blue-600 transition-colors">
                        Glasses Try-On
                    </button>
                    <button onclick="setActiveTab('objects')" id="objects-tab" class="text-gray-600 hover:text-blue-600 transition-colors">
                        Object Placement
                    </button>
                    <button onclick="setActiveTab('virtual-try-on')" id="virtual-try-on-tab" class="text-gray-600 hover:text-blue-600 transition-colors">
                        Virtual Try-On
                    </button>
                </nav>
                
                <div class="flex items-center space-x-4">
                    <button onclick="openTryOnModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
                        <i class="fas fa-camera"></i>
                        <span>Try On</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center">
            <h2 class="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
                Try Before You
                <span class="text-blue-600"> Buy</span>
            </h2>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Experience the future of shopping with our AI-powered virtual try-on technology. See how clothes look on you, 
                make confident purchase decisions, and upgrade your shopping experience.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="setActiveTab('clothes')" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2">
                    <i class="fas fa-tshirt"></i>
                    <span>Browse Clothes</span>
                </button>
                <button onclick="openTryOnModal()" class="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg hover:bg-blue-50 transition-colors">
                    Start Trying On
                </button>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <!-- Clothes Tab -->
        <div id="clothes-content" class="tab-content">
            <section class="mb-12">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">Available Clothes</h3>
                <p class="text-gray-600 mb-8">Click on any cloth to start virtual try-on</p>
                
                <!-- Clothes Grid -->
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6" id="clothes-grid">
                    {% if cloth_products %}
                        {% for product in cloth_products %}
                        <div class="cloth-card bg-white rounded-2xl shadow-sm overflow-hidden" 
                             data-cloth-image="{{ product.image }}" 
                             data-cloth-name="{{ product.name }}"
                             onclick="openTryOnWithCloth('{{ product.image }}', '{{ product.name }}')">
                            <div class="aspect-square bg-gray-100 relative group">
                                <img src="{{ url_for('static', filename=product.image) }}" 
                                     alt="{{ product.name }}" 
                                     class="w-full h-full object-cover">
                                                                 <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                     <div class="bg-white text-gray-800 px-4 py-2 rounded-lg text-sm font-medium">
                                         Try On
                                     </div>
                                 </div>
                                <div class="absolute top-4 right-4 bg-blue-600 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-check"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="text-sm font-semibold text-gray-800 truncate">{{ product.name }}</h4>
                                <div class="flex items-center justify-between mt-2">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <span class="text-xs text-gray-600">{{ "%.1f"|format(product.rating) }}</span>
                                    </div>
                                    <span class="text-sm font-bold text-blue-600">${{ "%.2f"|format(product.price) }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="col-span-full text-center py-12">
                            <i class="fas fa-tshirt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">No clothes available at the moment.</p>
                        </div>
                    {% endif %}
                </div>
            </section>
        </div>

        <!-- AR Furniture Tab -->
        <div id="ar-furniture-content" class="tab-content ar-furniture" style="display: none;">
            <section class="mb-12">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800">AR Furniture Placement</h3>
                    <div class="flex space-x-4">
                        <button id="start-ar-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
                            <i class="fas fa-cube"></i>
                            <span>Start AR</span>
                        </button>
                        <button onclick="window.arFurniture && window.arFurniture.clearPlacedObjects()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2">
                            <i class="fas fa-trash"></i>
                            <span>Clear All</span>
                        </button>
                    </div>
                </div>
                <p class="text-gray-600 mb-8">Select furniture and place it in your real environment using AR. Point your camera at a flat surface and tap to place.</p>

                <!-- Error Message -->
                <div id="ar-error-message" class="error-message" style="display: none;"></div>

                <!-- Feature Warning -->
                <div id="ar-feature-warning" class="feature-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    AR features require a compatible device and browser. Please use Chrome on Android or Safari on iOS.
                </div>

                <!-- AR Container -->
                <div class="ar-container mb-8">
                    <div id="ar-furniture-container" class="ar-canvas"></div>
                    <div id="furniture-loading" class="loading-overlay" style="display: none;">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">Loading 3D Models...</div>
                        </div>
                    </div>
                </div>

                <!-- Furniture Selection -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Select Furniture</h4>
                    <div id="furniture-selection" class="furniture-grid">
                        <!-- Furniture items will be populated by JavaScript -->
                    </div>
                </div>
            </section>
        </div>

        <!-- Glasses Try-On Tab -->
        <div id="glasses-tryon-content" class="tab-content glasses-tryon" style="display: none;">
            <section class="mb-12">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800">Virtual Glasses Try-On</h3>
                    <div class="flex space-x-4">
                        <button id="start-glasses-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
                            <i class="fas fa-video"></i>
                            <span>Start Camera</span>
                        </button>
                        <button onclick="window.glassesVTO && window.glassesVTO.captureScreenshot()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
                            <i class="fas fa-camera"></i>
                            <span>Capture</span>
                        </button>
                    </div>
                </div>
                <p class="text-gray-600 mb-8">Try on different glasses virtually using your camera. Allow camera access and select glasses to see how they look on you.</p>

                <!-- Error Message -->
                <div id="glasses-error-message" class="error-message" style="display: none;"></div>

                <!-- Glasses Container -->
                <div class="glasses-container mb-8">
                    <div class="webcam-container">
                        <video id="glasses-video" class="webcam-video" autoplay muted playsinline></video>
                        <div id="glasses-overlay" class="glasses-overlay"></div>
                        <canvas id="glasses-canvas" style="display: none;"></canvas>
                        <div id="glasses-loading" class="loading-overlay" style="display: none;">
                            <div class="text-center">
                                <div class="loading-spinner"></div>
                                <div class="loading-text">Loading Face Detection...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Glasses Selection -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Select Glasses</h4>
                    <div id="glasses-selection" class="glasses-grid">
                        <!-- Glasses items will be populated by JavaScript -->
                    </div>
                </div>
            </section>
        </div>

        <!-- Objects Tab -->
        <div id="objects-content" class="tab-content" style="display: none;">
            <section class="mb-12">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800">Place an Object in Your Scene</h3>
                    <button onclick="tryObjectFromURL()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
                        <i class="fas fa-link"></i>
                        <span>Try from URL</span>
                    </button>
                </div>
                <p class="text-gray-600 mb-8">Select an object from the list below, or provide a URL to your own object.</p>
                
                <!-- Objects Grid -->
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6" id="objects-grid">
                    {% if object_products %}
                        {% for product in object_products %}
                        <div class="cloth-card bg-white rounded-2xl shadow-sm overflow-hidden" 
                             data-object-image="{{ product.image }}" 
                             data-object-name="{{ product.name }}"
                             onclick="selectObjectForPlacement('{{ product.image }}', '{{ product.name }}')">
                            <div class="aspect-square bg-gray-100 relative group">
                                <img src="{{ url_for('static', filename=product.image) }}" 
                                     alt="{{ product.name }}" 
                                     class="w-full h-full object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                     <div class="bg-white text-gray-800 px-4 py-2 rounded-lg text-sm font-medium">
                                         Place this Object
                                     </div>
                                 </div>
                            </div>
                            <div class="p-4">
                                <h4 class="text-sm font-semibold text-gray-800 truncate">{{ product.name }}</h4>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="col-span-full text-center py-12">
                            <i class="fas fa-couch text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">No objects available for placement at the moment.</p>
                            <p class="text-gray-500 text-sm mt-2">Add images to the `client-side/static/images/objects` folder.</p>
                        </div>
                    {% endif %}
                </div>
            </section>
        </div>

        <!-- Virtual Try-On Tab -->
        <div id="virtual-try-on-content" class="tab-content" style="display: none;">
            <section class="mb-12">
                <div class="text-center mb-20">
                    <h1 class="sm:text-3xl text-2xl font-medium title-font text-gray-800 mb-4">Virtual Cloth Assistant</h1>
                    <p class="text-base leading-relaxed xl:w-2/4 lg:w-3/4 mx-auto text-gray-600">
                        Select a cloth from our collection and upload your photo to see how it looks on you!
                        Check out our AI-powered virtual try-on and get your wish fulfilled in seconds!!
                    </p>
                    <div class="flex mt-6 justify-center">
                        <div class="w-16 h-1 rounded-full bg-blue-500 inline-flex"></div>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="flex flex-wrap sm:-m-4 -mx-4 -mb-10 -mt-4 md:space-y-0 space-y-6 mb-12">
                    <div class="p-4 md:w-1/3 flex flex-col text-center items-center">
                        <div class="w-20 h-20 inline-flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-5 flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-2xl"></i>
                        </div>
                        <div class="flex-grow">
                            <h2 class="text-gray-800 text-lg title-font font-medium mb-3">The Problem</h2>
                            <p class="leading-relaxed text-base text-gray-600">
                                While buying clothes online, it is difficult for a customer to select a desirable outfit 
                                in the first attempt because they can't try on clothes before they are delivered physically.
                            </p>
                        </div>
                    </div>
                    <div class="p-4 md:w-1/3 flex flex-col text-center items-center">
                        <div class="w-20 h-20 inline-flex items-center justify-center rounded-full bg-green-100 text-green-600 mb-5 flex-shrink-0">
                            <i class="fas fa-lightbulb text-2xl"></i>
                        </div>
                        <div class="flex-grow">
                            <h2 class="text-gray-800 text-lg title-font font-medium mb-3">The Solution</h2>
                            <p class="leading-relaxed text-base text-gray-600">
                                E-commerce websites can be equipped with virtual trial rooms that allow users to try on 
                                multiple clothes virtually and select the best looking outfit in a single attempt.
                            </p>
                        </div>
                    </div>
                    <div class="p-4 md:w-1/3 flex flex-col text-center items-center">
                        <div class="w-20 h-20 inline-flex items-center justify-center rounded-full bg-purple-100 text-purple-600 mb-5 flex-shrink-0">
                            <i class="fas fa-brain text-2xl"></i>
                        </div>
                        <div class="flex-grow">
                            <h2 class="text-gray-800 text-lg title-font font-medium mb-3">The Approach</h2>
                            <p class="leading-relaxed text-base text-gray-600">
                                We used Deep Learning to solve this problem. VCA (virtual clothing assistant) for consumers, 
                                where users can select the cloth they want to wear and upload their image.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Try-On Section -->
                <div class="container mx-auto flex flex-col px-5 py-24 justify-center items-center">
                    <div class="w-full md:w-2/3 flex flex-col mb-16 items-center text-center">
                        <h1 class="title-font sm:text-4xl text-3xl mb-4 font-medium text-gray-800">
                            Select a cloth and upload your photo
                        </h1>
                        <p class="mb-8 leading-relaxed text-gray-600">
                            First, browse and select a cloth from our collection, then upload your photo to see the magic!
                        </p>
                        
                        <!-- Selected Cloth Display -->
                        <div id="selected-cloth-display" class="mb-8 p-6 bg-white rounded-2xl shadow-sm" style="display: none;">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Selected Cloth</h3>
                            <div class="flex items-center space-x-4">
                                <img id="selected-cloth-image" src="" alt="Selected Cloth" class="w-24 h-24 object-cover rounded-lg">
                                <div>
                                    <h4 id="selected-cloth-name" class="font-medium text-gray-800"></h4>
                                    <button onclick="clearSelectedCloth()" class="text-red-600 hover:text-red-700 text-sm">
                                        <i class="fas fa-times mr-1"></i>Change Selection
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Upload Section -->
                        <form action="{{ url_for('submit') }}" method="post" enctype="multipart/form-data" id="try-on-form">
                            <input type="hidden" id="cloth-image-input" name="cloth_image" value="">
                            
                            <div class="w-full max-w-md">
                                <label class="block text-lg font-medium text-gray-700 mb-4">Upload Your Photo</label>
                                <div class="file-upload-area flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                        <div class="flex text-sm text-gray-600">
                                            <input class="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" type="file" name="model" accept="image/*" required>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-8">
                                <button type="submit" id="try-on-btn" class="inline-flex text-white bg-blue-500 border-0 py-3 px-8 focus:outline-none hover:bg-blue-600 rounded text-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                    Try it
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Loading indicator -->
                    <div class="loading fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white p-8 rounded-lg text-center">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p class="text-gray-600">Processing your virtual try-on...</p>
                        </div>
                    </div>

                    <!-- Result Section -->
                    {% if op %}
                    <div class="bg-white rounded-2xl shadow-sm p-8 mt-8">
                        <center>
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">HERE'S YOUR RESULT 🤗</h2>
                            <div class="max-w-md mx-auto rounded-lg overflow-hidden shadow-lg">
                                <img alt="Virtual Try-on Result" class="w-full h-auto object-cover" src="data:image/png;base64,{{ op }}">
                            </div>
                            <div class="mt-6 flex justify-center space-x-4">
                                <button onclick="shareResult()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-share mr-2"></i>Share Result
                                </button>
                                <button onclick="tryAnother()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-redo mr-2"></i>Try Another
                                </button>
                            </div>
                        </center>
                    </div>
                    {% endif %}
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Modal -->
    <div id="loading-modal" class="modal">
        <div class="bg-white p-8 rounded-lg text-center flex items-center space-x-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p id="loading-message" class="text-gray-600">Processing...</p>
        </div>
    </div>

    <!-- Result Modal -->
    <div id="result-modal" class="modal">
        <div class="bg-white rounded-2xl p-8 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800">Result</h3>
                <button onclick="closeResultModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <p id="result-message" class="text-gray-600 mb-4"></p>
            <img id="result-image" src="" alt="Result Image" class="w-full h-auto object-cover rounded-lg">
        </div>
    </div>

    <!-- Virtual Try-On Modal -->
    <div id="try-on-modal" class="modal">
        <div class="bg-white rounded-2xl p-8 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800">Virtual Try-On</h3>
                <button onclick="closeTryOnModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="space-y-6">
                <!-- Selected Cloth Display in Modal -->
                <div id="modal-selected-cloth" class="p-4 bg-gray-50 rounded-lg" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selected Cloth</label>
                    <div class="flex items-center space-x-4">
                        <img id="modal-cloth-image" src="" alt="Selected Cloth" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h4 id="modal-cloth-name" class="font-medium text-gray-800"></h4>
                            <button onclick="changeCloth()" class="text-blue-600 hover:text-blue-700 text-sm">
                                <i class="fas fa-edit mr-1"></i>Change Cloth
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Cloth Selection (shown when no cloth is selected) -->
                <div id="cloth-selection-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Select a Cloth</label>
                    <p class="text-sm text-gray-600 mb-4">Please browse our clothes collection and select one for try-on</p>
                    <button onclick="setActiveTab('clothes')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Browse Clothes
                    </button>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Upload Your Photo</label>
                    <div class="file-upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                        <input type="file" id="modal-model-input" accept="image/*" class="hidden">
                        <button type="button" onclick="document.getElementById('modal-model-input').click()" class="text-blue-600 hover:text-blue-700">
                            Choose your photo
                        </button>
                        <p class="text-xs text-gray-500 mt-1">PNG, JPG up to 10MB</p>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-4">
                    <button onclick="closeTryOnModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                        Cancel
                    </button>
                    <button onclick="submitTryOn()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Try On
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-eye text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold">TryOn Vibe</h3>
                    </div>
                    <p class="text-gray-400">
                        Experience the future of shopping with AI-powered virtual try-on technology and intelligent recommendations.
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Products</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Clothes</a></li>
                        <li><a href="#" class="hover:text-white">Jewelry</a></li>
                        <li><a href="#" class="hover:text-white">Beauty</a></li>
                        <li><a href="#" class="hover:text-white">Accessories</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Virtual Try-On</a></li>
                        <li><a href="#" class="hover:text-white">AI Recommendations</a></li>
                        <li><a href="#" class="hover:text-white">Real-time Processing</a></li>
                        <li><a href="#" class="hover:text-white">Smart Fitting</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Help Center</a></li>
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 TryOn Vibe. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Hidden file input for object placement -->
    <input type="file" id="background-image-input" class="hidden" accept="image/*">

    <script>
        const tabs = ['clothes', 'objects', 'virtual-try-on'];
        const content = ['clothes-content', 'objects-content', 'virtual-try-on-content'];
        let selectedCloth = null;
        let selectedObjectForPlacement = null;

        function setActiveTab(tabName) {
            tabs.forEach(tab => {
                document.getElementById(`${tab}-tab`).classList.remove('text-blue-600');
                document.getElementById(`${tab}-tab`).classList.add('text-gray-600');
            });
            content.forEach(c => {
                document.getElementById(c).style.display = 'none';
            });

            document.getElementById(`${tabName}-tab`).classList.add('text-blue-600');
            document.getElementById(`${tabName}-tab`).classList.remove('text-gray-600');
            document.getElementById(`${tabName}-content`).style.display = 'block';
        }

        // --- Modal Functions ---
        function showLoadingModal(message = 'Processing...') {
            document.getElementById('loading-message').textContent = message;
            document.getElementById('loading-modal').classList.add('active');
        }

        function hideLoadingModal() {
            document.getElementById('loading-modal').classList.remove('active');
        }

        function closeResultModal() {
            document.getElementById('result-modal').classList.remove('active');
        }
        
        function openTryOnModal() {
            document.getElementById('try-on-modal').classList.add('active');
        }

        function closeTryOnModal() {
            document.getElementById('try-on-modal').classList.remove('active');
        }

        function showError(message) {
            alert(message); // A simple error display
        }

        // --- Cloth Try-On Functions ---
        function openTryOnWithCloth(imagePath, name) {
            selectedCloth = { image: imagePath, name: name };
            document.getElementById('modal-cloth-image').src = `{{ url_for('static', filename='') }}${imagePath}`;
            document.getElementById('modal-cloth-name').textContent = name;
            document.getElementById('modal-selected-cloth').style.display = 'block';
            document.getElementById('cloth-selection-section').style.display = 'none';
            openTryOnModal();
        }

        function changeCloth() {
            selectedCloth = null;
            document.getElementById('modal-selected-cloth').style.display = 'none';
            document.getElementById('cloth-selection-section').style.display = 'block';
            closeTryOnModal();
            setActiveTab('clothes');
        }

        async function submitTryOn() {
            const modelInput = document.getElementById('modal-model-input');
            if (!selectedCloth) {
                showError('Please select a cloth first.');
                return;
            }
            if (modelInput.files.length === 0) {
                showError('Please upload a model image.');
                return;
            }

            const formData = new FormData();
            formData.append('cloth_image', selectedCloth.image);
            formData.append('model', modelInput.files[0]);

            closeTryOnModal();
            showLoadingModal('Performing virtual try-on...');

            try {
                const response = await fetch('/preds', {
                    method: 'POST',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' },
                    body: formData
                });
                const result = await response.json();
                if (result.success) {
                    document.getElementById('result-image').src = `data:image/png;base64,${result.image_data}`;
                    document.getElementById('result-message').textContent = result.message;
                    document.getElementById('result-modal').classList.add('active');
                } else {
                    showError(result.message);
                }
            } catch (error) {
                showError('An error occurred while processing the try-on.');
            } finally {
                hideLoadingModal();
            }
        }

        // --- Object Placement Functions ---
        function selectObjectForPlacement(objectImagePath, objectName) {
            selectedObjectForPlacement = { path: objectImagePath, name: objectName };
            document.getElementById('background-image-input').click();
        }

        document.getElementById('background-image-input').addEventListener('change', async function(event) {
            if (event.target.files && event.target.files[0] && selectedObjectForPlacement) {
                const backgroundImageFile = event.target.files[0];
                
                const formData = new FormData();
                formData.append('background_image', backgroundImageFile);
                formData.append('object_image', selectedObjectForPlacement.path);

                showLoadingModal('Placing object with AI...');

                try {
                    const response = await fetch('/place-object', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        document.getElementById('result-image').src = `data:image/jpeg;base64,${result.image_data}`;
                        document.getElementById('result-message').textContent = result.message;
                        document.getElementById('result-modal').classList.add('active');
                    } else {
                        showError(result.message || 'An unknown error occurred.');
                    }
                } catch (error) {
                    console.error('Error during object placement:', error);
                    showError('Could not connect to the server. Please try again.');
                } finally {
                    hideLoadingModal();
                    event.target.value = '';
                    selectedObjectForPlacement = null;
                }
            }
        });

        // Initialize default tab
        document.addEventListener('DOMContentLoaded', () => {
            setActiveTab('clothes');
            initializeARFeatures();
        });

        // Initialize AR Features
        async function initializeARFeatures() {
            try {
                // Wait a bit for all scripts to load
                await new Promise(resolve => setTimeout(resolve, 500));

                // Initialize AR Furniture
                if (window.arFurniture) {
                    await window.arFurniture.init();
                }

                // Initialize Glasses VTO
                if (window.glassesVTO) {
                    await window.glassesVTO.init();
                    window.glassesVTO.setupCameraControls();
                }

                // Check feature support and show warnings
                checkFeatureSupport();
            } catch (error) {
                console.error('Failed to initialize AR features:', error);
            }
        }

        // Check feature support
        async function checkFeatureSupport() {
            // Check WebXR support
            const webxrSupport = await window.threeUtils.checkWebXRSupport();
            if (!webxrSupport.supported) {
                const warning = document.getElementById('ar-feature-warning');
                if (warning) {
                    warning.style.display = 'block';
                    warning.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>WebXR not supported: ${webxrSupport.reason}`;
                }
            }
        }

        // Enhanced tab switching to handle AR features
        function setActiveTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.style.display = 'none');

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('nav button');
            tabs.forEach(tab => {
                tab.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'font-medium');
                tab.classList.add('text-gray-600');
            });

            // Show selected tab content
            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            // Add active class to selected tab
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.remove('text-gray-600');
                selectedTab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'font-medium');
            }

            // Handle AR-specific initialization
            if (tabName === 'ar-furniture' && window.arFurniture) {
                // Resize AR container if needed
                setTimeout(() => {
                    window.threeUtils.handleResize('ar-furniture-container');
                }, 100);
            } else if (tabName === 'glasses-tryon' && window.glassesVTO) {
                // Resize glasses overlay if needed
                setTimeout(() => {
                    window.threeUtils.handleResize('glasses-overlay');
                }, 100);
            }
        }

        function tryObjectFromURL() {
            const imageUrl = prompt("Please enter the public URL of the object's image:");
            if (!imageUrl) return; // User cancelled

            const productName = prompt("What is the name of this object? (e.g., 'Blue Sofa', 'Gold Watch')");
            if (!productName) return; // User cancelled

            showLoadingModal('Downloading and removing background...');

            fetch('/process-image-from-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    image_url: imageUrl,
                    product_name: productName 
                })
            })
            .then(response => response.json())
            .then(result => {
                hideLoadingModal();
                if (result.success) {
                    // Use a timeout to ensure the UI has updated before triggering the next step
                    setTimeout(() => {
                        selectObjectForPlacement(result.object_path, 'Custom Object from URL');
                    }, 100);
                } else {
                    showError(result.message || 'Failed to process image from the provided URL.');
                }
            })
            .catch(error => {
                hideLoadingModal();
                console.error('Error processing image from URL:', error);
                showError('An error occurred while trying to process the image from the URL.');
            });
        }

        function shareResult() {
            alert("Share functionality not implemented yet.");
        }
    </script>

    <!-- AR Feature Scripts -->
    <script src="{{ url_for('static', filename='js/three-utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ar-furniture.js') }}"></script>
    <script src="{{ url_for('static', filename='js/glasses-vto.js') }}"></script>
</body>
</html>