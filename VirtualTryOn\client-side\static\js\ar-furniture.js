/**
 * AR Furniture Module for VirtualTryOn
 * Handles WebXR furniture placement and AR interactions
 */

class ARFurniture {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.reticle = null;
        this.controller = null;
        this.hitTestSource = null;
        this.hitTestSourceRequested = false;
        this.furnitureModels = new Map();
        this.placedObjects = [];
        this.selectedModelIndex = 0;
        this.isARActive = false;
        
        // Furniture catalog - will be loaded from API
        this.furnitureItems = [];
    }

    /**
     * Load furniture catalog from API
     */
    async loadFurnitureCatalog() {
        try {
            const response = await fetch('/api/furniture-catalog');
            const furnitureData = await response.json();
            this.furnitureItems = furnitureData;
            console.log('Loaded furniture catalog:', this.furnitureItems.length, 'items');
        } catch (error) {
            console.error('Failed to load furniture catalog:', error);
            // Fallback to empty array
            this.furnitureItems = [];
        }
    }

    /**
     * Initialize AR Furniture system
     */
    async init() {
        try {
            // Check WebXR support
            const webxrSupport = await window.threeUtils.checkWebXRSupport();
            if (!webxrSupport.supported) {
                this.showError('WebXR not supported: ' + webxrSupport.reason);
                return false;
            }

            // Load furniture catalog
            await this.loadFurnitureCatalog();

            // Initialize Three.js scene
            this.initScene();

            // Setup furniture selection UI
            this.setupFurnitureSelection();

            // Preload some models
            await this.preloadModels();
            
            console.log('AR Furniture system initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize AR Furniture:', error);
            this.showError('Failed to initialize AR system: ' + error.message);
            return false;
        }
    }

    /**
     * Initialize Three.js scene for AR
     */
    initScene() {
        const container = document.getElementById('ar-furniture-container');
        if (!container) {
            throw new Error('AR furniture container not found');
        }

        // Create scene with AR-specific settings
        const { scene, camera, renderer } = window.threeUtils.createScene('ar-furniture-container', {
            alpha: true,
            shadowMap: true,
            lighting: false // We'll add AR lighting
        });

        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;

        // Enable XR
        this.renderer.xr.enabled = true;

        // Create reticle for placement indication
        this.reticle = window.threeUtils.createReticle();
        this.scene.add(this.reticle);

        // Setup XR lighting
        this.setupXRLighting();

        // Setup AR controls
        this.setupARControls();
    }

    /**
     * Setup XR lighting with estimation
     */
    setupXRLighting() {
        // Basic lighting for non-AR mode
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        this.scene.add(directionalLight);

        // XR Estimated Light (will be added when AR session starts)
        if (typeof XREstimatedLight !== 'undefined') {
            this.xrLight = new XREstimatedLight(this.renderer);
            
            this.xrLight.addEventListener('estimationstart', () => {
                this.scene.add(this.xrLight);
                this.scene.remove(ambientLight);
                this.scene.remove(directionalLight);
                
                if (this.xrLight.environment) {
                    this.scene.environment = this.xrLight.environment;
                }
            });

            this.xrLight.addEventListener('estimationend', () => {
                this.scene.add(ambientLight);
                this.scene.add(directionalLight);
                this.scene.remove(this.xrLight);
                this.scene.environment = null;
            });
        }
    }

    /**
     * Setup AR controls and interaction
     */
    setupARControls() {
        // Setup controller for object placement
        this.controller = this.renderer.xr.getController(0);
        this.controller.addEventListener('select', this.onSelect.bind(this));
        this.scene.add(this.controller);

        // Setup AR button
        this.createARButton();
    }

    /**
     * Create AR button for starting AR session
     */
    createARButton() {
        const arButton = document.getElementById('start-ar-btn');
        if (!arButton) return;

        arButton.addEventListener('click', async () => {
            if (this.isARActive) {
                this.stopAR();
            } else {
                await this.startAR();
            }
        });
    }

    /**
     * Start AR session
     */
    async startAR() {
        try {
            // Check device capabilities first
            const capabilities = window.threeUtils.getDeviceCapabilities();
            if (!capabilities.isHTTPS && location.hostname !== 'localhost') {
                throw new Error('HTTPS is required for AR features');
            }

            if (!capabilities.isMobile) {
                console.warn('AR works best on mobile devices');
            }

            const session = await navigator.xr.requestSession('immersive-ar', {
                requiredFeatures: ['hit-test'],
                optionalFeatures: ['dom-overlay', 'light-estimation'],
                domOverlay: { root: document.body }
            });

            await this.renderer.xr.setSession(session);
            this.isARActive = true;

            // Start performance monitoring
            window.performanceMonitor.start();

            // Update UI
            const arButton = document.getElementById('start-ar-btn');
            if (arButton) {
                arButton.textContent = 'Stop AR';
                arButton.classList.add('bg-red-500');
                arButton.classList.remove('bg-blue-500');
            }

            // Start render loop
            this.renderer.setAnimationLoop(this.render.bind(this));

            console.log('AR session started');
        } catch (error) {
            console.error('Failed to start AR session:', error);
            let errorMessage = 'Failed to start AR: ';

            if (error.message.includes('HTTPS')) {
                errorMessage += 'HTTPS is required for AR features.';
            } else if (error.message.includes('not supported')) {
                errorMessage += 'AR is not supported on this device or browser.';
            } else {
                errorMessage += error.message;
            }

            this.showError(errorMessage);
        }
    }

    /**
     * Stop AR session
     */
    stopAR() {
        if (this.renderer.xr.getSession()) {
            this.renderer.xr.getSession().end();
        }

        this.isARActive = false;
        this.hitTestSource = null;
        this.hitTestSourceRequested = false;

        // Stop performance monitoring
        window.performanceMonitor.stop();

        // Update UI
        const arButton = document.getElementById('start-ar-btn');
        if (arButton) {
            arButton.textContent = 'Start AR';
            arButton.classList.remove('bg-red-500');
            arButton.classList.add('bg-blue-500');
        }

        console.log('AR session stopped');
    }

    /**
     * Handle object placement on select
     */
    onSelect() {
        if (this.reticle.visible && this.selectedModelIndex < this.furnitureItems.length) {
            this.placeFurniture();
        }
    }

    /**
     * Place furniture at reticle position
     */
    async placeFurniture() {
        const furnitureItem = this.furnitureItems[this.selectedModelIndex];
        
        try {
            let model = this.furnitureModels.get(furnitureItem.id);
            
            if (!model) {
                // Load model if not cached
                const gltf = await window.threeUtils.loadGLTFModel(furnitureItem.model);
                model = gltf.scene;
                this.furnitureModels.set(furnitureItem.id, model.clone());
            } else {
                model = model.clone();
            }

            // Position at reticle location
            model.position.setFromMatrixPosition(this.reticle.matrix);
            model.scale.setScalar(0.5); // Adjust scale as needed
            
            this.scene.add(model);
            this.placedObjects.push(model);
            
            console.log(`Placed ${furnitureItem.name} at position:`, model.position);
        } catch (error) {
            console.error('Failed to place furniture:', error);
            this.showError('Failed to place furniture: ' + error.message);
        }
    }

    /**
     * Render loop for AR
     */
    render(timestamp, frame) {
        if (frame) {
            const referenceSpace = this.renderer.xr.getReferenceSpace();
            const session = this.renderer.xr.getSession();

            // Setup hit test source
            if (!this.hitTestSourceRequested) {
                session.requestReferenceSpace('viewer').then((referenceSpace) => {
                    session.requestHitTestSource({ space: referenceSpace }).then((source) => {
                        this.hitTestSource = source;
                    });
                });

                session.addEventListener('end', () => {
                    this.hitTestSourceRequested = false;
                    this.hitTestSource = null;
                });

                this.hitTestSourceRequested = true;
            }

            // Update reticle position
            if (this.hitTestSource) {
                const hitTestResults = frame.getHitTestResults(this.hitTestSource);

                if (hitTestResults.length) {
                    const hit = hitTestResults[0];
                    this.reticle.visible = true;
                    this.reticle.matrix.fromArray(hit.getPose(referenceSpace).transform.matrix);
                } else {
                    this.reticle.visible = false;
                }
            }
        }

        this.renderer.render(this.scene, this.camera);
    }

    /**
     * Setup furniture selection UI
     */
    setupFurnitureSelection() {
        const container = document.getElementById('furniture-selection');
        if (!container) return;

        container.innerHTML = '';
        
        this.furnitureItems.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'furniture-item';
            itemElement.innerHTML = `
                <img src="${item.preview}" alt="${item.name}" onerror="this.src='/static/images/placeholder.png'">
                <div class="name">${item.name}</div>
            `;
            
            itemElement.addEventListener('click', () => {
                this.selectFurniture(index);
            });
            
            container.appendChild(itemElement);
        });
        
        // Select first item by default
        this.selectFurniture(0);
    }

    /**
     * Select furniture item
     */
    selectFurniture(index) {
        this.selectedModelIndex = index;
        
        // Update UI selection
        const items = document.querySelectorAll('.furniture-item');
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }

    /**
     * Preload some furniture models
     */
    async preloadModels() {
        const loadingIndicator = document.getElementById('furniture-loading');
        if (loadingIndicator) loadingIndicator.style.display = 'block';
        
        try {
            // Load first few models
            for (let i = 0; i < Math.min(3, this.furnitureItems.length); i++) {
                const item = this.furnitureItems[i];
                const gltf = await window.threeUtils.loadGLTFModel(item.model);
                this.furnitureModels.set(item.id, gltf.scene);
            }
        } catch (error) {
            console.warn('Failed to preload some models:', error);
        } finally {
            if (loadingIndicator) loadingIndicator.style.display = 'none';
        }
    }

    /**
     * Clear all placed objects
     */
    clearPlacedObjects() {
        this.placedObjects.forEach(object => {
            this.scene.remove(object);
        });
        this.placedObjects = [];
    }

    /**
     * Show error message
     */
    showError(message) {
        const errorContainer = document.getElementById('ar-error-message');
        if (errorContainer) {
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
        }
        console.error('AR Furniture Error:', message);
    }

    /**
     * Cleanup resources
     */
    dispose() {
        this.clearPlacedObjects();
        this.furnitureModels.clear();
        
        if (this.isARActive) {
            this.stopAR();
        }
        
        window.threeUtils.dispose('ar-furniture-container');
    }
}

// Create global instance
window.arFurniture = new ARFurniture();
