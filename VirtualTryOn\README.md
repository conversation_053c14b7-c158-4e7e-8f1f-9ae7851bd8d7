# 🚀 VirtualTryOn Platform - Complete AR & AI-Powered Try-On Experience

## 📋 Project Overview

This is a **comprehensive AI-powered Virtual Try-On platform** with multiple AR and AI capabilities:

1. **Flask Web App** (`client-side/`) - Modern web interface with multiple try-on modes
2. **AI Server** - Deep learning models for virtual clothing try-on (designed for Google Colab)
3. **AR Furniture Placement** - WebXR-powered 3D furniture placement in real environments
4. **Virtual Glasses Try-On** - Real-time face detection and glasses overlay using TensorFlow.js
5. **Universal Object Placement** - AI-driven object placement using Google Gemini

## ✨ New AR Features

### 🪑 AR Furniture Placement
- **WebXR Integration**: Place 3D furniture models in your real environment using AR
- **Hit Testing**: Accurate surface detection for realistic placement
- **8 Furniture Models**: Chairs, tables, lamps, and more
- **Mobile AR Support**: Works on Chrome (Android) and Safari (iOS)

### 👓 Virtual Glasses Try-On
- **Real-time Face Detection**: TensorFlow.js-powered face landmark detection
- **Live Camera Feed**: Try on glasses using your device camera
- **Multiple Glasses Types**: Sunglasses, eyeglasses, and more
- **Responsive Design**: Works on desktop and mobile devices

### 🎯 Enhanced Object Placement
The existing object placement feature has been enhanced with better AI integration and expanded catalog support.

## ✨ Universal Object Placement

This feature allows you to place any object into a scene using a sophisticated AI-driven workflow. You can try out furniture in your room, see how a watch looks on your wrist, or place any other object imaginable.

### How It Works

The system uses a multi-stage process to ensure accurate and realistic object placement:

1.  **Object Input**: Users can select from pre-loaded objects or provide a direct URL to an image of any object.
2.  **Background Removal**: If a URL is provided, the backend downloads the image and uses the `rembg` library to automatically remove the background, isolating the object.
3.  **Contextual Analysis**: The application analyzes the object's name (e.g., "necklace," "lamp," "tv") to generate a specific contextual hint.
4.  **Hybrid AI Placement**:
    *   **For Eyeglasses**: A custom computer vision model using OpenCV's Haar Cascades first detects the precise location of the user's eyes. This coordinate data is then passed to Gemini.
    *   **For All Other Objects**: The image, object description, and contextual hint are sent to the **Google Gemini 2.0 Flash** model.
5.  **Intelligent Scaling & Positioning**: Gemini analyzes the scene's perspective, depth, and existing objects to determine the most realistic position, scale, and rotation for the new object.
6.  **Image Overlay**: The final object is overlaid onto the background image using the coordinates provided by the AI.

### Technology Stack

-   **Backend**: Flask with enhanced API endpoints
-   **3D Rendering**: Three.js for WebXR and glasses overlay
-   **AR Technology**: WebXR for furniture placement
-   **Face Detection**: TensorFlow.js with MediaPipe Face Mesh
-   **Generative AI**: Google Gemini 2.0 Flash for object placement
-   **Computer Vision**: OpenCV (for eye detection)
-   **Background Removal**: `rembg`
-   **Image Processing**: Pillow
-   **Frontend**: Modern responsive design with Tailwind CSS

## 🎯 Quick Start Options

### Option 1: Demo Mode (Easiest - Works Immediately)
```bash
# 1. Install dependencies
pip install flask pillow requests gunicorn python-dotenv google-generativeai numpy rembg onnxruntime opencv-python-headless

# 2. Run the Flask app in demo mode
cd client-side
python app.py
```

Visit `http://localhost:5001` - the app will work with:
- ✅ AR Furniture placement (requires compatible device)
- ✅ Virtual Glasses Try-On (requires camera access)
- ✅ Object placement with Gemini AI (requires GEMINI_API_KEY)
- 🎭 Clothing try-on in demo mode (just returns the model image)

### Option 2: Full Setup with Google Colab AI Server (Recommended)

[![Open in colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/khusburai28/VirtualTryOn/blob/main/setup_ngrok.ipynb)


1. Set up AI server on Google Colab (see instructions below)
2. Get your ngrok URL from Colab
3. Set environment variable: `export AI_SERVER_URL="your-ngrok-url"`
4. Set: `export DEMO_MODE=false`
5. Run the Flask app

### Option 3: Local AI Server Setup (Advanced)
Set up all AI dependencies locally (requires significant setup).

## 🛠️ Detailed Setup Instructions

### Prerequisites
- Python 3.8+
- Virtual environment (recommended)
- For local AI: CUDA GPU, OpenPose, Human Parsing models

### Browser Compatibility & Requirements

#### AR Furniture Placement
- **Mobile**: Chrome on Android, Safari on iOS (with WebXR support)
- **Desktop**: Chrome with WebXR flags enabled
- **Requirements**: HTTPS (except localhost), WebXR support

#### Virtual Glasses Try-On
- **All Platforms**: Chrome, Firefox, Safari, Edge (modern versions)
- **Requirements**: Camera access, HTTPS (except localhost)

#### General Requirements
- **HTTPS**: Required for camera access and WebXR (except localhost)
- **Permissions**: Camera access for glasses try-on
- **Performance**: Modern device with WebGL support recommended

### 1. Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install flask pillow requests gunicorn python-dotenv
```

### 2. Google Colab AI Server Setup (Recommended)

1. **Upload notebook to Google Colab:**
   - Open Google Colab
   - Upload `setup_ngrok.ipynb` from your project

2. **Run the notebook:**
   - Execute all cells in order
   - The notebook will:
     - Install dependencies (OpenPose, models, etc.)
     - Download pre-trained models
     - Set up ngrok tunnel
     - Start the AI server

3. **Get your ngrok URL:**
   - After running the notebook, you'll get a URL like:
     `https://abc123-xyz.ngrok-free.app`

4. **Configure your Flask app:**
   ```bash
   export AI_SERVER_URL="https://your-ngrok-url.ngrok-free.app/api/transform"
   export DEMO_MODE=false
   ```

### 3. Running the Flask App

```bash
cd client-side
python app.py
```

The app will show:
- 🎭 Demo mode: If no AI server is configured
- 🤖 Production mode: If AI server URL is set

### 4. Local AI Server Setup (Advanced)

If you want to run everything locally:

1. **Install system dependencies:**
   ```bash
   # Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install build-essential libopencv-dev
   sudo apt-get install libatlas-base-dev libprotobuf-dev libleveldb-dev
   
   # macOS
   brew install opencv cmake protobuf
   ```

2. **Install OpenPose:**
   ```bash
   git clone https://github.com/CMU-Perceptual-Computing-Lab/openpose.git
   cd openpose
   mkdir build && cd build
   cmake ..
   make -j`nproc`
   ```

3. **Download models:**
   - Download pre-trained models from the Google Drive links in the notebooks
   - Place them in `checkpoints/` directory

4. **Install Python dependencies:**
   ```bash
   pip install torch torchvision torchaudio
   pip install torchgeometry opencv-python
   pip install -r requirements.txt
   ```

## 🔧 Configuration Options

### Environment Variables
```bash
# AI Server URL (required for production mode for clothes)
export AI_SERVER_URL="https://your-ngrok-url.ngrok-free.app/api/transform"

# Gemini API Key (required for the new Object Placement feature)
export GEMINI_API_KEY="your-gemini-api-key-here"

# Demo mode (true/false)
export DEMO_MODE=true

# Flask configuration
export FLASK_ENV=development
export FLASK_SECRET_KEY=your-secret-key-here
```

### Project Structure
```
VirtualTryOn/
├── client-side/              # Flask web application
│   ├── app.py               # Enhanced Flask app with AR APIs
│   ├── templates/
│   │   └── index.html       # Multi-tab interface with AR features
│   ├── static/
│   │   ├── js/              # AR JavaScript modules
│   │   │   ├── three-utils.js      # Three.js utilities
│   │   │   ├── ar-furniture.js     # WebXR furniture placement
│   │   │   └── glasses-vto.js      # TensorFlow.js glasses try-on
│   │   ├── css/
│   │   │   ├── style.css           # Original styles
│   │   │   └── ar-styles.css       # AR-specific styles
│   │   ├── models/
│   │   │   ├── furniture/          # 3D furniture models (.glb)
│   │   │   └── glasses/            # Glasses textures
│   │   └── images/
│   │       ├── furniture/          # Furniture preview images
│   │       ├── glasses/            # Glasses preview images
│   │       └── objects/            # Object placement images
│   ├── eye_detector.py      # OpenCV eye detection
│   ├── gemini_placer.py     # Gemini AI integration
│   └── requirements.txt     # Enhanced dependencies
├── ar-components/           # Modular AR components
├── assets/                  # Sample images
├── checkpoints/             # Pre-trained models (download required)
├── networks/                # Neural network definitions
├── test.py                  # ML inference script
├── setup_ngrok.ipynb        # Google Colab setup
├── setup_gradio.ipynb       # Alternative Gradio setup
└── plans.md                 # Integration plan documentation
```

## 🚀 Usage Instructions

### Web Interface Navigation
The platform features a tabbed interface with multiple try-on modes:

#### 👕 Clothes Virtual Try-On
1. **Browse Clothes:** View available clothing items
2. **Upload Model Image:** Select a photo of a person
3. **Select Clothing:** Click on any clothing item to try it on
4. **View Results:** See the virtual try-on result

#### 🪑 AR Furniture Placement
1. **Select Furniture:** Choose from 8 available 3D furniture models
2. **Start AR:** Click "Start AR" button (requires compatible device)
3. **Point Camera:** Aim at a flat surface (floor, table, etc.)
4. **Place Furniture:** Tap when the reticle appears to place furniture
5. **Multiple Items:** Place multiple furniture pieces in your space

#### 👓 Virtual Glasses Try-On
1. **Start Camera:** Click "Start Camera" and allow camera access
2. **Select Glasses:** Choose from available glasses styles
3. **Real-time Try-On:** See glasses overlaid on your face in real-time
4. **Capture Photo:** Take a screenshot of your try-on result

### Universal Object Placement
1.  **Navigate to the "Object Placement" Tab**: Here you can see pre-loaded objects.
2.  **Add Your Own Objects**:
    *   **From File**: Add your own `.png` or `.jpg` images to the `client-side/static/images/objects` folder. They will appear automatically.
    *   **From URL**: Click the "Try from URL" button, paste a direct image link, and give your product a name.
3.  **Select an Object**: Click on any object in the grid.
4.  **Upload a Scene**: You will be prompted to upload a background image (e.g., a photo of your room or yourself).
5.  **View the Result**: The AI will place the object in your scene, and the final image will be displayed in a modal.

### API Endpoints
- `GET /` - Main web interface with all features
- `POST /preds` - Process clothing virtual try-on request
- `POST /place-object` - AI-powered object placement
- `POST /process-image-from-url` - Process image from URL for object placement
- `GET /api/products` - Get sample product catalog
- `GET /api/cloth-images` - Get available clothing items
- `GET /api/furniture-catalog` - Get 3D furniture models catalog
- `GET /api/glasses-catalog` - Get glasses collection
- `GET /api/ar-support` - Check AR feature support
- `GET /api/health` - Health check endpoint

## 🐛 Troubleshooting

### Common Issues

1. **"ModuleNotFoundError: No module named 'flask'"**
   ```bash
   pip install flask pillow requests
   ```

2. **"Cannot connect to AI server"**
   - Check if your ngrok URL is correct
   - Ensure the Google Colab notebook is running
   - Verify the AI server is accessible

3. **"Demo mode active"**
   - This is normal if no AI server is configured
   - Set `DEMO_MODE=false` and provide valid `AI_SERVER_URL`

4. **Images not uploading**
   - Check file formats (PNG, JPG, JPEG, GIF only)
   - Verify file size limits
   - Ensure upload directory exists

5. **AR Furniture not working**
   - Ensure you're using a compatible device (mobile recommended)
   - Check that you're on HTTPS (required for WebXR)
   - Try Chrome on Android or Safari on iOS
   - Point camera at a well-lit flat surface

6. **Glasses Try-On not working**
   - Allow camera permissions when prompted
   - Ensure you're on HTTPS (required for camera access)
   - Check that your face is well-lit and visible
   - Try refreshing the page if face detection fails

7. **"WebXR not supported" error**
   - Use Chrome on Android or Safari on iOS
   - Enable WebXR flags in Chrome desktop (chrome://flags)
   - Ensure device has AR capabilities

8. **Camera permission denied**
   - Check browser settings for camera permissions
   - Ensure no other apps are using the camera
   - Try refreshing the page and allowing permissions again

### Performance Tips
- Use GPU for AI processing (Google Colab provides free GPU)
- Optimize image sizes (recommended: 768x1024)
- Use ngrok for stable tunneling from Colab

## 🔗 External Dependencies

### Required for Full Setup:
- **OpenPose**: Human pose estimation
- **Human Parsing Models**: Body part segmentation
- **Pre-trained Models**: Virtual try-on neural networks
- **ngrok**: Tunneling service for Colab

### Model Downloads:
The setup notebooks automatically download:
- Segmentation model (`seg_final.pth`)
- Geometric matching model (`gmm_final.pth`)
- ALIAS generator model (`alias_final.pth`)
- Cloth segmentation model (`cloth_segm_u2net_latest.pth`)

## 📝 Next Steps

1. **Start with Demo Mode** to test the web interface
2. **Set up Google Colab** for full AI functionality
3. **Customize the UI** by editing templates and CSS
4. **Add your own products** by modifying the product catalog
5. **Deploy to production** using gunicorn or similar

---

**Happy Virtual Try-On! 🎉** 